import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { projectId } = await params;

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    // Verify user has access to this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        organization_id,
        project_members!inner(user_id)
      `)
      .eq('id', projectId)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId},project_members.user_id.eq.${userId}`)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Get terminology entries for this project
    const { data: terminology, error: terminologyError } = await supabase
      .from('terminology_entries')
      .select('approval_status, target_language, category, created_at')
      .eq('project_id', projectId);

    if (terminologyError) {
      console.error('Terminology fetch error:', terminologyError);
      return NextResponse.json(
        { error: 'Failed to fetch terminology', success: false },
        { status: 500 }
      );
    }

    // Calculate statistics
    const totalEntries = terminology?.length || 0;
    const approvedEntries = terminology?.filter(t => t.approval_status === 'approved').length || 0;
    const pendingEntries = terminology?.filter(t => t.approval_status === 'pending').length || 0;
    const rejectedEntries = terminology?.filter(t => t.approval_status === 'rejected').length || 0;

    // Calculate by language
    const languagesCount: Record<string, number> = {};
    terminology?.forEach(t => {
      languagesCount[t.target_language] = (languagesCount[t.target_language] || 0) + 1;
    });

    // Calculate by category
    const categoriesCount: Record<string, number> = {};
    terminology?.forEach(t => {
      const category = t.category || 'general';
      categoriesCount[category] = (categoriesCount[category] || 0) + 1;
    });

    // Calculate consistency score (approved / total)
    const consistencyScore = totalEntries > 0 ? approvedEntries / totalEntries : 0;

    // Determine if terminology setup is complete
    const isSetupComplete = totalEntries > 0 && pendingEntries === 0;
    const canStartTranslation = isSetupComplete && consistencyScore >= 0.8; // 80% approved

    // Determine next step
    let nextStep = 'setup_terminology';
    if (totalEntries > 0 && pendingEntries > 0) {
      nextStep = 'review_pending_terms';
    } else if (totalEntries > 0 && consistencyScore < 0.8) {
      nextStep = 'improve_consistency';
    } else if (canStartTranslation) {
      nextStep = 'ready_for_translation';
    }

    return NextResponse.json({
      success: true,
      data: {
        totalEntries,
        approvedEntries,
        pendingEntries,
        rejectedEntries,
        languagesCount,
        categoriesCount,
        consistencyScore,
        isSetupComplete,
        canStartTranslation,
        nextStep,
      },
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
